<template>
    <div class="education-main">
        <div class="tab-container">
            <div class="custom-tabs-header">
                <div class="back-button" @click="goBack">
                    <i class="el-icon-arrow-left"></i>
                </div>
                <el-tabs v-model="activeTab" class="education-tabs education-tabs--large" @tab-click="handleTabClick">
                    <el-tab-pane label="迈瑞图书馆" name="library"></el-tab-pane>
                    <el-tab-pane label="直播培训" name="live_training"></el-tab-pane>
                    <el-tab-pane label="瑞影·AI+" name="ai_plus"></el-tab-pane>
                    <el-tab-pane label="病例库" name="case_database"></el-tab-pane>
                    <el-tab-pane label="云考核" name="cloud_exam"></el-tab-pane>
                    <el-tab-pane label="技能认证" name="skill_certification"></el-tab-pane>
                </el-tabs>
            </div>

            <div class="tab-content">
                <keep-alive>
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";

export default {
    mixins: [base],
    name: "EducationMain",
    components: {},
    data() {
        return {
            activeTab: "library",
            lastActiveTab: "library",
        };
    },
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                this.setActiveTabByRoute(to.path);
            },
        },
    },
    created() {
        this.setActiveTabByRoute(this.$route.path);
    },
    methods: {
        goBack() {
            const cid = this.$route.params.cid;
            if (cid) {
                // 从聊天窗口进入的，返回聊天窗口
                this.$router.replace(`/main/index/chat_window/${cid}`);
            } else {
                // 直接访问的，返回主页面
                this.$router.replace('/main/dashboard');
            }
        },
        setActiveTabByRoute(path) {
            if (path.includes("/education/library")) {
                this.activeTab = "library";
                this.lastActiveTab = "library";
            } else if (path.includes("/education/live_training")) {
                this.activeTab = "live_training";
                this.lastActiveTab = "live_training";
            } else if (path.includes("/education/ai_plus")) {
                this.activeTab = "ai_plus";
                this.lastActiveTab = "ai_plus";
            } else if (path.includes("/education/case_database")) {
                this.activeTab = "case_database";
                this.lastActiveTab = "case_database";
            } else if (path.includes("/education/cloud_exam")) {
                this.activeTab = "cloud_exam";
                this.lastActiveTab = "cloud_exam";
            } else if (path.includes("/education/skill_certification")) {
                this.activeTab = "skill_certification";
                this.lastActiveTab = "skill_certification";
            }
        },
        async handleTabClick(tab) {
            const cid = this.$route.params.cid;
            if(tab.name === this.lastActiveTab){
                return
            }

            let targetRoute;
            if (cid) {
                // 从聊天窗口进入的路由
                targetRoute = `/main/index/chat_window/${cid}/education/${tab.name}`;
            } else {
                // 直接访问的路由
                targetRoute = `/main/education/${tab.name}`;
            }

            await Tool.loadModuleRouter(targetRoute);
            this.lastActiveTab = tab.name;
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-tabs-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 10px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;

        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
}

.education-tabs {
    flex: 1;
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.coming-soon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
